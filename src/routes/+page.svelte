<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { writable } from 'svelte/store';
	import Logo from '$lib/Logo.svelte';
	import { websiteContent } from '$lib/content';
	import NavigationDots from '$lib/components/NavigationDots.svelte';

	// GSAP will be imported dynamically in onMount for better performance
	let gsap: any;
	let ScrollToPlugin: any;
	let Observer: any;

	// Full-page scroll state
	const sectionIds = ['hero', 'philosophy', 'services', 'contact'];
	let currentSectionIndex = 0;
	let isAnimating = false;
	const activeSection = writable(sectionIds[0]);

	// Reactive state for Services tabs (Svelte 5 syntax)
	let activeTab = $state('traditional');

	// Store keyboard handler reference for cleanup
	let keydownHandler: ((e: KeyboardEvent) => void) | null = null;

	function scrollToSection(id: string) {
		const index = sectionIds.indexOf(id);
		if (index === -1 || isAnimating) return;

		currentSectionIndex = index;
		isAnimating = true;
		activeSection.set(id);

		gsap.to(window, {
			scrollTo: { y: `#${id}`, autoKill: false },
			duration: 1.2,
			ease: 'power3.inOut',
			onComplete: () => {
				isAnimating = false;
				// Trigger content animation for the new section
				playContentAnimation(id);
			}
		});
	}

	onMount(async () => {
		// Dynamic import of GSAP for better performance
		const gsapModule = await import('gsap');
		const scrollToPluginModule = await import('gsap/ScrollToPlugin');
		const observerModule = await import('gsap/Observer');

		gsap = gsapModule.gsap;
		ScrollToPlugin = scrollToPluginModule.ScrollToPlugin;
		Observer = observerModule.Observer;

		gsap.registerPlugin(ScrollToPlugin, Observer);

		// Initialize all section animations in reset state
		resetAllAnimations();

		// Play initial hero animation
		playHeroAnimation();

		// Set up Observer for full-page scroll
		Observer.create({
			target: window,
			type: 'wheel,touch,pointer',
			wheelSpeed: -1,
			onUp: () => {
				if (currentSectionIndex > 0 && !isAnimating) {
					scrollToSection(sectionIds[currentSectionIndex - 1]);
				}
			},
			onDown: () => {
				if (currentSectionIndex < sectionIds.length - 1 && !isAnimating) {
					scrollToSection(sectionIds[currentSectionIndex + 1]);
				}
			},
			tolerance: 10,
			preventDefault: true
		});

		// Add keyboard navigation
		keydownHandler = (e: KeyboardEvent) => {
			if (isAnimating) return;

			switch (e.key) {
				case 'ArrowUp':
				case 'PageUp':
					e.preventDefault();
					if (currentSectionIndex > 0) {
						scrollToSection(sectionIds[currentSectionIndex - 1]);
					}
					break;
				case 'ArrowDown':
				case 'PageDown':
				case ' ': // Spacebar
					e.preventDefault();
					if (currentSectionIndex < sectionIds.length - 1) {
						scrollToSection(sectionIds[currentSectionIndex + 1]);
					}
					break;
				case 'Home':
					e.preventDefault();
					scrollToSection(sectionIds[0]);
					break;
				case 'End':
					e.preventDefault();
					scrollToSection(sectionIds[sectionIds.length - 1]);
					break;
			}
		};

		window.addEventListener('keydown', keydownHandler);
	});

	function playContentAnimation(sectionId: string) {
		switch (sectionId) {
			case 'hero':
				playHeroAnimation();
				break;
			case 'philosophy':
				playPhilosophyAnimation();
				break;
			case 'services':
				playServicesAnimation();
				break;
			case 'contact':
				playCTAAnimation();
				break;
		}
	}

	function resetAllAnimations() {
		if (!gsap) return;

		// Reset all sections to initial state
		gsap.set('.hero-logo, .hero-headline, .hero-subheadline, .hero-cta', { opacity: 0, y: 30 });
		gsap.set('.philosophy-heading, .philosophy-paragraph, .philosophy-visual', { opacity: 0, y: 30 });
		gsap.set('.services-header, .services-tabs, .service-item', { opacity: 0, y: 30 });
		gsap.set('.cta-heading, .cta-button', { opacity: 0, y: 30 });
	}

	onDestroy(() => {
		// Clean up GSAP Observer instances
		if (Observer) {
			Observer.getAll().forEach((observer: any) => observer.kill());
		}

		// Remove keyboard event listener
		if (keydownHandler) {
			window.removeEventListener('keydown', keydownHandler);
		}
	});

	function switchTab(tab: 'traditional' | 'tech') {
		if (activeTab === tab || !gsap) return;

		// Animate out current content
		gsap.to('.tab-content', {
			duration: 0.3,
			opacity: 0,
			y: 20,
			ease: 'power2.out',
			onComplete: () => {
				activeTab = tab;
				// Animate in new content
				gsap.fromTo('.tab-content',
					{ opacity: 0, y: 20 },
					{ duration: 0.4, opacity: 1, y: 0, ease: 'power2.out' }
				);
			}
		});
	}

	function playHeroAnimation() {
		if (!gsap) return;

		// Hero section load-in sequence
		const tl = gsap.timeline();

		tl.to('.hero-logo', {
			duration: 0.8,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
		.to('.hero-headline', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		}, '-=0.4')
		.to('.hero-subheadline', {
			duration: 0.8,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		}, '-=0.6')
		.to('.hero-cta', {
			duration: 0.6,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		}, '-=0.4');
	}

	function playPhilosophyAnimation() {
		if (!gsap) return;

		// Philosophy section animation sequence
		const tl = gsap.timeline();

		tl.to('.philosophy-heading', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
		.to('.philosophy-paragraph', {
			duration: 0.8,
			y: 0,
			opacity: 1,
			stagger: 0.2,
			ease: 'power2.out'
		}, '-=0.6')
		.to('.philosophy-visual', {
			duration: 1,
			y: 0,
			opacity: 1,
			scale: 1,
			ease: 'power2.out'
		}, '-=0.8');
	}

	function playServicesAnimation() {
		if (!gsap) return;

		// Services section animation sequence
		const tl = gsap.timeline();

		tl.to('.services-header', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
		.to('.services-tabs', {
			duration: 0.8,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		}, '-=0.6')
		.to('.service-item', {
			duration: 0.6,
			y: 0,
			opacity: 1,
			stagger: 0.1,
			ease: 'power2.out'
		}, '-=0.4');
	}

	function playCTAAnimation() {
		if (!gsap) return;

		// CTA section animation sequence
		const tl = gsap.timeline();

		tl.to('.cta-heading', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		})
		.to('.cta-button', {
			duration: 0.8,
			y: 0,
			opacity: 1,
			ease: 'power2.out'
		}, '-=0.6');
	}
</script>

<!-- Navigation Dots -->
<NavigationDots sections={sectionIds} {activeSection} {scrollToSection} />

<!-- Hero Section -->
<section id="hero" class="h-screen w-full flex flex-col items-center justify-center text-center p-8 bg-background relative">
	<div class="hero-logo">
		<Logo class="h-16 w-16 mx-auto mb-8 text-primary" />
	</div>

	<h1 class="hero-headline font-display text-5xl lg:text-7xl font-bold text-heading tracking-tighter">
		{websiteContent.hero.headline}
	</h1>

	<p class="hero-subheadline mt-6 text-lg lg:text-xl text-content max-w-2xl mx-auto">
		{websiteContent.hero.subHeadline}
	</p>

	<button
		onclick={() => scrollToSection('contact')}
		class="hero-cta mt-10 inline-block bg-primary hover:bg-primary-hover text-white font-bold py-4 px-10 rounded-lg transition-colors duration-300"
	>
		{websiteContent.hero.ctaText}
	</button>
</section>

<!-- Philosophy Section -->
<section id="philosophy" class="h-screen w-full flex items-center justify-center bg-background relative">
	<div class="container mx-auto px-8 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
		<!-- Left Column: Text Content -->
		<div>
			<h2 class="philosophy-heading font-display text-4xl lg:text-5xl font-bold text-heading tracking-tighter">
				{websiteContent.philosophy.heading}
			</h2>

			{#each websiteContent.philosophy.paragraphs as paragraph}
				<p class="philosophy-paragraph mt-6 text-lg text-content">
					{paragraph}
				</p>
			{/each}
		</div>

		<!-- Right Column: Visual Element -->
		<div class="philosophy-visual flex items-center justify-center">
			<div class="w-80 h-80 relative">
				<!-- Abstract animated visual -->
				<div class="absolute inset-0 bg-gradient-to-br from-violet-500 to-violet-600 rounded-full opacity-20 animate-pulse"></div>
				<div class="absolute inset-4 bg-gradient-to-tl from-violet-600 to-violet-500 rounded-full opacity-30 animate-pulse" style="animation-delay: 0.5s;"></div>
				<div class="absolute inset-8 bg-violet-500 rounded-full opacity-40 animate-pulse" style="animation-delay: 1s;"></div>
			</div>
		</div>
	</div>
</section>

<!-- Services Section -->
<section id="services" class="h-screen w-full flex items-center justify-center bg-slate-50 relative">
	<div class="container mx-auto px-8 max-h-screen overflow-y-auto">
		<!-- Header Area -->
		<div class="services-header text-center max-w-3xl mx-auto mb-8">
			<h2 class="text-4xl font-bold text-heading tracking-tight">
				{websiteContent.services.heading}
			</h2>
			<p class="mt-4 text-lg text-content">
				{websiteContent.services.subHeading}
			</p>
		</div>

		<!-- Tab Navigation -->
		<div class="services-tabs flex justify-center border-b border-subtle mb-8">
			<button
				onclick={() => switchTab('traditional')}
				class="px-6 py-3 font-bold -mb-px border-b-2 transition-colors duration-300 {activeTab === 'traditional' ? 'border-primary text-primary' : 'border-transparent text-content hover:text-primary'}"
			>
				{websiteContent.services.tabs.traditional.label}
			</button>
			<button
				onclick={() => switchTab('tech')}
				class="px-6 py-3 font-bold -mb-px border-b-2 transition-colors duration-300 {activeTab === 'tech' ? 'border-primary text-primary' : 'border-transparent text-content hover:text-primary'}"
			>
				{websiteContent.services.tabs.tech.label}
			</button>
		</div>

		<!-- Tab Content Panels -->
		<div class="tab-content">
			{#if activeTab === 'traditional'}
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{#each websiteContent.services.tabs.traditional.services as service}
						<div class="service-item bg-white p-6 rounded-lg shadow-sm border border-subtle hover:shadow-md transition-shadow duration-300">
							<h3 class="font-bold text-heading text-lg mb-3">
								{service.title}
							</h3>
							<p class="text-content">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			{/if}

			{#if activeTab === 'tech'}
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{#each websiteContent.services.tabs.tech.services as service}
						<div class="service-item bg-white p-6 rounded-lg shadow-sm border border-subtle hover:shadow-md transition-shadow duration-300">
							<h3 class="font-bold text-heading text-lg mb-3">
								{service.title}
							</h3>
							<p class="text-content">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</section>

<!-- Call to Action Section -->
<section id="contact" class="h-screen w-full flex items-center justify-center bg-background text-center relative">
	<div class="container mx-auto px-8">
		<h2 class="cta-heading font-display text-4xl lg:text-5xl font-bold text-heading tracking-tighter">
			{websiteContent.cta.heading}
		</h2>

		<a
			href="mailto:<EMAIL>"
			class="cta-button mt-10 inline-block bg-primary hover:bg-primary-hover text-white font-bold py-4 px-10 rounded-lg transition-colors duration-300"
		>
			{websiteContent.cta.buttonText}
		</a>
	</div>
</section>
