<script lang="ts">
	import type { Writable } from 'svelte/store';

	interface Props {
		sections: string[];
		activeSection: Writable<string>;
		scrollToSection: (id: string) => void;
	}

	let { sections, activeSection, scrollToSection }: Props = $props();
</script>

<nav class="fixed top-1/2 -translate-y-1/2 left-8 hidden lg:flex flex-col gap-4 z-50">
	<ul class="flex flex-col gap-4">
		{#each sections as sectionId, i (sectionId)}
			<li>
				<button
					onclick={() => scrollToSection(sectionId)}
					class="block w-3 h-3 rounded-full transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 {sectionId === $activeSection ? 'bg-primary h-5' : 'bg-subtle hover:bg-primary'}"
					aria-label="Go to section {i + 1}"
				></button>
			</li>
		{/each}
	</ul>
</nav>

<!-- Mobile navigation indicator -->
<div class="fixed bottom-8 left-1/2 -translate-x-1/2 lg:hidden z-50">
	<div class="flex gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
		{#each sections as sectionId, i (sectionId)}
			<button
				onclick={() => scrollToSection(sectionId)}
				class="w-2 h-2 rounded-full transition-all duration-300 {sectionId === $activeSection ? 'bg-primary w-6' : 'bg-subtle'}"
				aria-label="Go to section {i + 1}"
			></button>
		{/each}
	</div>
</div>
